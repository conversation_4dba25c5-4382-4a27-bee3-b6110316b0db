<template>
<div class="music-list">
    <div class="title">
        <a href="javascript:;">推荐歌单 <ArrowRight class="ArrowRight"/></a>
    </div>
    <div class="container-wrapper">
        <button
            class="nav-arrow nav-arrow-left"
            @click="previousPage"
            :disabled="currentPage === 0"
            v-show="totalPages > 1"
        >
            <ArrowLeft />
        </button>

        <div class="flex-container" ref="containerRef">
            <div class="flex-item" v-for="(column, columnIndex) in visibleColumns" :key="columnIndex">
                <a href="javascript:;" class="item-content" v-for="item in column" :key="item.id">
                    {{ item.name }}
                </a>
            </div>
        </div>

        <button
            class="nav-arrow nav-arrow-right"
            @click="nextPage"
            :disabled="currentPage >= totalPages - 1"
            v-show="totalPages > 1"
        >
            <ArrowRight />
        </button>
    </div>
</div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import {
  ArrowRight,
  ArrowLeft,
} from '@element-plus/icons-vue'

// 音乐列表数据
const musiclist = ref([
  {
    id: 1,
    name: '推荐歌单1',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 2,
    name: '推荐歌单2',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 3,
    name: '推荐歌单3',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 4,
    name: '推荐歌单4',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 5,
    name: '推荐歌单5',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 6,
    name: '推荐歌单6',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 7,
    name: '推荐歌单7',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 8,
    name: '推荐歌单8',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 9,
    name: '推荐歌单9',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 10,
    name: '推荐歌单10',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 11,
    name: '推荐歌单11',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 12,
    name: '推荐歌单12',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 13,
    name: '推荐歌单13',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
  {
    id: 14,
    name: '推荐歌单14',
    cover: 'https://p2.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg',
  },
])

// 翻页相关状态
const currentPage = ref(0)
const containerRef = ref<HTMLElement>()
const columnsPerPage = ref(4) // 每页显示的列数，默认4列

// 将数据按列分组，每列4个元素
const musicColumns = computed(() => {
  const columns = []
  const itemsPerColumn = 4

  for (let i = 0; i < musiclist.value.length; i += itemsPerColumn) {
    columns.push(musiclist.value.slice(i, i + itemsPerColumn))
  }

  return columns
})

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(musicColumns.value.length / columnsPerPage.value)
})

// 当前页显示的列
const visibleColumns = computed(() => {
  const startIndex = currentPage.value * columnsPerPage.value
  const endIndex = startIndex + columnsPerPage.value
  return musicColumns.value.slice(startIndex, endIndex)
})

// 翻页方法
const nextPage = () => {
  if (currentPage.value < totalPages.value - 1) {
    currentPage.value++
  }
}

const previousPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--
  }
}

// 计算每页应该显示多少列（基于容器宽度）
const calculateColumnsPerPage = () => {
  if (!containerRef.value) return

  const containerWidth = containerRef.value.offsetWidth
  const minColumnWidth = 270 + 15 // 最小宽度 + gap
  const maxColumns = Math.floor(containerWidth / minColumnWidth)

  // 限制最大显示列数，避免显示过多列
  columnsPerPage.value = Math.min(maxColumns, 4)

  // 如果当前页超出了新的总页数，调整到最后一页
  if (currentPage.value >= totalPages.value) {
    currentPage.value = Math.max(0, totalPages.value - 1)
  }
}

// 监听窗口大小变化
onMounted(() => {
  nextTick(() => {
    calculateColumnsPerPage()
  })

  window.addEventListener('resize', calculateColumnsPerPage)
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateColumnsPerPage)
})

</script>

<style scoped>

.music-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title {
  /* font-size: 18px; */
  /* font-weight: bold; */
  /* margin-bottom: 10px; */
  padding-bottom: 5px;
}
.title a {
  font-size: 18px;
  font-weight: bold;
  color: #000;
  text-decoration: none;
}
.ArrowRight {
    width: 18px;
    height: 18px;
}

/* 容器包装器 - 包含导航箭头和内容容器 */
.container-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
}

/* 导航箭头样式 */
.nav-arrow {
  background: rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  flex-shrink: 0;
}

.nav-arrow:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}

.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-arrow-left {
  margin-right: 15px;
}

.nav-arrow-right {
  margin-left: 15px;
}

/* 弹性盒子容器 - 固定高度，不换行 */
.flex-container {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 15px;
  width: 100%;
  height: 220px; /* 固定高度，4个元素 * 55px */
  overflow: hidden;
}

/* 每个弹性盒子项 - 纵向排列，固定高度220px(4个元素*55px)，宽度自适应，最少270px */
.flex-item {
  display: flex;
  flex-direction: column;
  height: 220px; /* 4个元素 * 55px */
  min-width: 270px;
  flex: 1;
  flex-shrink: 0;
  gap: 0;
  padding: 0;
  align-items: stretch;
  /* background-color: #f8f9fa; */
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); */
}
.flex-item a {
    text-decoration: none;
    color: #000;
}

/* 盒子内的内容元素 */
.item-content {
  padding: 8px 12px;
  height: 55px; /* 固定高度55px */
  flex: none;
  box-sizing: border-box;
  font-size: 14px;

  /* background-color: #ffffff; */

  border-top: 1px solid #e0e0e0;

  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all 0.3s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 第一个元素不需要上边框 */
.item-content:first-child {
  border-top: none;
}

/* .item-content:hover {
  background-color: #f0f0f0;
  border-color: #d0d0d0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
} */

</style>